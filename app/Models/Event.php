<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Event extends Model
{
    protected $primaryKey = 'event_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'event_id',
        'event_name',
        'start',
        'end',
        'end_subscribe',
        'club_id',
        'level',
        'sex',
        'prize',
        'currency',
        'sponsor1',
        'sponsor2',
        'sponsor3',
        'subscription'
    ];

    protected $casts = [
        'start' => 'date',
        'end' => 'date',
        'end_subscribe' => 'date',
        'level' => 'integer',
        'sex' => 'integer',
        'prize' => 'decimal:2',
        'subscription' => 'decimal:2',
    ];

    /**
     * Generate unique event ID
     * Format: 6 characters alphanumeric (letters and numbers)
     */
    public static function generateEventId(): string
    {
        do {
            // Generate 6 random alphanumeric characters
            $eventId = strtoupper(Str::random(6));

            // Ensure at least one letter and one number
            while (!preg_match('/[A-Z]/', $eventId) || !preg_match('/[0-9]/', $eventId)) {
                $eventId = strtoupper(Str::random(6));
            }
        } while (self::where('event_id', $eventId)->exists());

        return $eventId;
    }

    /**
     * Get the club that owns this event
     */
    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }

    /**
     * Get the first sponsor
     */
    public function firstSponsor()
    {
        return $this->belongsTo(Sponsor::class, 'sponsor1', 'sponsor_id');
    }

    /**
     * Get the second sponsor
     */
    public function secondSponsor()
    {
        return $this->belongsTo(Sponsor::class, 'sponsor2', 'sponsor_id');
    }

    /**
     * Get the third sponsor
     */
    public function thirdSponsor()
    {
        return $this->belongsTo(Sponsor::class, 'sponsor3', 'sponsor_id');
    }

    /**
     * Get all sponsors for this event
     */
    public function sponsors()
    {
        $sponsors = collect();
        
        if ($this->sponsor1) {
            $sponsors->push($this->firstSponsor);
        }
        if ($this->sponsor2) {
            $sponsors->push($this->secondSponsor);
        }
        if ($this->sponsor3) {
            $sponsors->push($this->thirdSponsor);
        }
        
        return $sponsors->filter();
    }

    /**
     * Get event subscriptions
     */
    public function subscriptions()
    {
        return $this->hasMany(EventSubscribe::class, 'event_id', 'event_id');
    }

    /**
     * Get subscribed players
     */
    public function subscribedPlayers()
    {
        return $this->belongsToMany(Player::class, 'events_subscribe', 'event_id', 'player_id')
                    ->withPivot('subsc_date')
                    ->withTimestamps();
    }

    /**
     * Get level text
     */
    public function getLevelTextAttribute()
    {
        $levels = [
            1 => 'Beginner',
            2 => 'Intermediate', 
            3 => 'Advanced',
            4 => 'Professional'
        ];

        return $levels[$this->level] ?? 'All Levels';
    }

    /**
     * Get sex text
     */
    public function getSexTextAttribute()
    {
        $sexes = [
            1 => 'Male',
            2 => 'Female'
        ];

        return $sexes[$this->sex] ?? 'Mixed';
    }

    /**
     * Check if event is active (subscription period is open)
     */
    public function getIsActiveAttribute()
    {
        return now()->lte($this->end_subscribe);
    }

    /**
     * Check if event has started
     */
    public function getHasStartedAttribute()
    {
        return now()->gte($this->start);
    }

    /**
     * Check if event has ended
     */
    public function getHasEndedAttribute()
    {
        return now()->gt($this->end);
    }

    /**
     * Get event status
     */
    public function getStatusAttribute()
    {
        if ($this->has_ended) {
            return 'Completed';
        } elseif ($this->has_started) {
            return 'In Progress';
        } elseif ($this->is_active) {
            return 'Open for Registration';
        } else {
            return 'Registration Closed';
        }
    }

    /**
     * Get subscription count
     */
    public function getSubscriptionCountAttribute()
    {
        return $this->subscriptions()->count();
    }
}
