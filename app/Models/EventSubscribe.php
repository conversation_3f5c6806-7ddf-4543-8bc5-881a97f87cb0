<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EventSubscribe extends Model
{
    protected $table = 'events_subscribe';
    
    // Composite primary key
    protected $primaryKey = ['event_id', 'player_id'];
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'event_id',
        'player_id',
        'subsc_date'
    ];

    protected $casts = [
        'subsc_date' => 'date',
    ];

    /**
     * Set the keys for a save update query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function setKeysForSaveQuery($query)
    {
        $keys = $this->getKeyName();
        if (!is_array($keys)) {
            return parent::setKeysForSaveQuery($query);
        }

        foreach ($keys as $keyName) {
            $query->where($keyName, '=', $this->getKeyForSaveQuery($keyName));
        }

        return $query;
    }

    /**
     * Get the primary key value for a save query.
     *
     * @param  mixed  $keyName
     * @return mixed
     */
    protected function getKeyForSaveQuery($keyName = null)
    {
        if (is_null($keyName)) {
            $keyName = $this->getKeyName();
        }

        if (isset($this->original[$keyName])) {
            return $this->original[$keyName];
        }

        return $this->getAttribute($keyName);
    }

    /**
     * Get the event that owns this subscription
     */
    public function event()
    {
        return $this->belongsTo(Event::class, 'event_id', 'event_id');
    }

    /**
     * Get the player that owns this subscription
     */
    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }
}
