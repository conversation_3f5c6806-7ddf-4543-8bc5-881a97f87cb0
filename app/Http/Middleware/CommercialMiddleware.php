<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CommercialMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated as commercial (web session)
        if (!Auth::guard('commercial_web')->check()) {
            // If it's an AJAX request, return JSON response
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Commercial access required.'
                ], 403);
            }

            // Redirect to commercial subdomain login for web requests
            $commercialLoginUrl = 'https://commercial.' . parse_url(config('app.url'), PHP_URL_HOST);
            return redirect($commercialLoginUrl)->with('error', 'Unauthorized. Commercial access required.');
        }

        return $next($request);
    }
}
