<?php

namespace App\Livewire\Auth;

use Illuminate\Support\Facades\Password;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('components.layouts.auth')]
class AdminForgotPassword extends Component
{
    public string $email = '';

    /**
     * Send a password reset link to the provided email address.
     */
    public function sendPasswordResetLink(): void
    {
        $this->validate([
            'email' => ['required', 'string', 'email'],
        ]);

        // Use the admin password broker
        $status = Password::broker('admins')->sendResetLink($this->only('email'));

        if ($status === Password::RESET_LINK_SENT) {
            session()->flash('status', __('A reset link has been sent to your email address.'));
        } else {
            session()->flash('status', __('A reset link will be sent if the account exists.'));
        }
    }

    public function render()
    {
        return view('livewire.auth.admin-forgot-password');
    }
}
