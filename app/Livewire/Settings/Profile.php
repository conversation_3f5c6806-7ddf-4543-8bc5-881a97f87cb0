<?php

namespace App\Livewire\Settings;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;
use Livewire\Component;

class Profile extends Component
{
    public string $name = '';

    public string $email = '';

    public bool $disableEmailUpdate = true;

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        if (Auth::guard('club_web')->check()) {
            $this->name = Auth::guard('club_web')->user()->club_name;
            $this->email = Auth::guard('club_web')->user()->email;
        } elseif (Auth::guard('commercial_web')->check()) {
            $this->name = Auth::guard('commercial_web')->user()->name;
            $this->email = Auth::guard('commercial_web')->user()->email;
        } else {
            $this->name = Auth::user()->name;
            $this->email = Auth::user()->email;
            $this->disableEmailUpdate = false;
        }
    }

    /**
     * Update the profile information for the currently authenticated user.
     */
    public function updateProfileInformation(): void
    {
        $user = null;
        $userType = 'admin';
        if (Auth::guard('club_web')->check()) {
            $user = Auth::guard('club_web')->user();
            $userType = 'club';
        } elseif (Auth::guard('commercial_web')->check()) {
            $user = Auth::guard('commercial_web')->user();
        } else {
            $user = Auth::user();
        }

        if ($this->disableEmailUpdate) {
            $validated = $this->validate([
                'name' => ['required', 'string', 'max:255'],
            ]);
        } else {
            $validated = $this->validate([
                'name' => ['required', 'string', 'max:255'],

                'email' => [
                    'required',
                    'string',
                    'lowercase',
                    'email',
                    'max:255',
                    Rule::unique(User::class)->ignore($user->id),
                ],
            ]);
        }

        if($userType == 'club'){
            $user->update(['club_name' => $validated['name']]);
        }else{
            $user->fill($validated);
        }

        // if ($user->isDirty('email')) {
        //     $user->email_verified_at = null;
        // }

        $user->save();

        $this->dispatch('profile-updated', name: $user->name);
    }

    /**
     * Send an email verification notification to the current user.
     */
    public function resendVerificationNotification(): void
    {
        $user = Auth::user();

        if ($user->hasVerifiedEmail()) {
            $this->redirectIntended(default: route('dashboard', absolute: false));

            return;
        }

        $user->sendEmailVerificationNotification();

        Session::flash('status', 'verification-link-sent');
    }
}
