<?php

namespace App\Livewire\Invoicing;

use App\Models\Commercial;
use App\Models\Club;
use App\Models\Token;
use App\Models\Payment;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CommercialInvoicing extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';

    // Payment properties
    public $showPaymentModal = false;
    public $selectedCommercialForPayment = null;
    public $paymentType = 0; // 0 = Wire, 1 = Cash

    // Club details modal properties
    public $showClubDetailsModal = false;
    public $selectedCommercialForDetails = null;
    public $clubDetails = [];

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Commercial::query();

        // Check if user is a commercial (logged in via commercial_web guard)
        if (Auth::guard('commercial_web')->check()) {
            // If commercial user, show only their own data
            $commercialUser = Auth::guard('commercial_web')->user();
            $query->where('commercial_id', $commercialUser->commercial_id);
        } else {
            // Admin user - show all commercials with search and filters
            if ($this->search) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('commercial_id', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            }

            if ($this->status !== '') {
                $query->where('status', $this->status);
            }
        }

        $commercials = $query->orderBy('inserted', 'desc')->paginate(10);

        // Calculate statistics for each commercial
        foreach ($commercials as $commercial) {
            $commercial->stats = $this->calculateCommercialStats($commercial);
            $commercial->clubBreakdown = $this->calculateClubBreakdown($commercial);
        }

        return view('livewire.invoicing.commercial-invoicing', [
            'commercials' => $commercials,
            'isCommercialUser' => Auth::guard('commercial_web')->check()
        ]);
    }

    private function calculateCommercialStats($commercial)
    {
        $stats = [
            'purchased' => 0,
            'purchased_with_commission' => 0,
            'redeemed' => 0,
            'redeemed_with_commission' => 0,
            'paid_redeemed' => 0,
            'paid_redeemed_with_commission' => 0,
            'paid_amounts' => 0,
            'paid_amounts_with_commission' => 0,
            'balance' => 0,
        ];

        // Get all clubs for this commercial
        $clubIds = Club::where('commercial_id', $commercial->commercial_id)
            ->pluck('club_id')
            ->toArray();

        if (empty($clubIds)) {
            return $stats;
        }

        // Get all tokens for clubs under this commercial
        $tokens = Token::whereIn('club_id', $clubIds)
            ->with(['tokenType', 'club'])
            ->get(['token_id', 'token_type_id', 'club_id', 'status', 'purchased_at', 'paid_at']);

        foreach ($tokens as $token) {
            $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
            // $club = $token->club;
            // $commercialCommission = $club ? ($club->commercial_commission / 100) : 0;

            // $adjustedValueWithCommison = $tokenValue * $commercialCommission;
            
            $adjustedValue = $tokenValue;

            // Purchased tokens (purchased_at is not null)
            if ($token->purchased_at) {
                $stats['purchased'] += $adjustedValue;
                // $stats['purchased_with_commission'] += $adjustedValueWithCommison;
            }

            // Redeemed tokens (status = 'Redeemed')
            if ($token->status === 'Redeemed') {
                $stats['redeemed'] += $adjustedValue;
                // $stats['redeemed_with_commission'] += $adjustedValueWithCommison;
            }

            // Paid-Redeemed tokens (purchased and paid, then redeemed)
            if ($token->purchased_at && $token->paid_at && $token->status === 'Redeemed') {
                $stats['paid_redeemed'] += $adjustedValue;
                // $stats['paid_redeemed_with_commission'] += $adjustedValueWithCommison;
            }

            // Paid amounts (tokens that have been paid)
            if ($token->paid_at) {
                $stats['paid_amounts'] += $adjustedValue;
                // $stats['paid_amounts_with_commission'] += $adjustedValueWithCommison;
            }
        }

        // Calculate balance: (Purchased + Paid-Redeemed - Redeemed - Paid Amounts)
        $stats['balance'] = ($stats['purchased'] + $stats['paid_redeemed'] - $stats['redeemed'] - $stats['paid_amounts']) * 0.03;

        return $stats;
    }

    private function calculateClubBreakdown($commercial)
    {
        $clubBreakdown = [];

        // Get all clubs for this commercial
        $clubs = Club::where('commercial_id', $commercial->commercial_id)
            ->with(['tokens.tokenType'])
            ->get();

        foreach ($clubs as $club) {
            $clubStats = [
                'club_id' => $club->club_id,
                'club_name' => $club->club_name,
                'purchased' => 0,
                'redeemed' => 0,
                'paid_redeemed' => 0,
                'paid_amounts' => 0,
                'balance' => 0,
            ];

            foreach ($club->tokens as $token) {
                $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;

                // Purchased tokens (purchased_at is not null)
                if ($token->purchased_at) {
                    $clubStats['purchased'] += $tokenValue;
                }

                // Redeemed tokens (status = 'Redeemed')
                if ($token->status === 'Redeemed') {
                    $clubStats['redeemed'] += $tokenValue;
                }

                // Paid-Redeemed tokens (purchased and paid, then redeemed)
                if ($token->purchased_at && $token->paid_at && $token->status === 'Redeemed') {
                    $clubStats['paid_redeemed'] += $tokenValue;
                }

                // Paid amounts (tokens that have been paid)
                if ($token->paid_at) {
                    $clubStats['paid_amounts'] += $tokenValue;
                }
            }

            // Calculate balance: (Purchased + Paid-Redeemed - Redeemed - Paid Amounts) * 3%
            $clubStats['balance'] = ($clubStats['purchased'] + $clubStats['paid_redeemed'] - $clubStats['redeemed'] - $clubStats['paid_amounts']) * 0.03;

            // Only include clubs that have some activity
            if ($clubStats['purchased'] > 0 || $clubStats['redeemed'] > 0 || $clubStats['paid_redeemed'] > 0 || $clubStats['paid_amounts'] > 0) {
                $clubBreakdown[] = $clubStats;
            }
        }

        return $clubBreakdown;
    }

    public function toggleCommercialStatus($commercialId)
    {
        $commercial = Commercial::findOrFail($commercialId);
        $newStatus = $commercial->status === 1 ? 0 : 1;

        $commercial->update(['status' => $newStatus]);

        $statusText = $newStatus === 1 ? 'Active' : 'Inactive';
        $this->dispatch('notify', variant: 'success', title: 'Commercial Status Changed', message: "Commercial status changed to {$statusText}.");
    }

    /**
     * Show payment modal for commercial
     */
    public function payCommercial($commercialId)
    {
        $commercial = Commercial::findOrFail($commercialId);

        // Check if there are any tokens to pay for this commercial
        $clubIds = Club::where('commercial_id', $commercialId)->pluck('club_id')->toArray();
        
        if (empty($clubIds)) {
            $this->dispatch('notify', variant: 'danger', title: 'No Clubs Found', message: 'This commercial has no associated clubs.');
            return;
        }

        $tokensToPayCount = Token::whereIn('club_id', $clubIds)
            ->whereNotNull('purchased_at')
            ->whereNull('paid_at')
            ->count();

        if ($tokensToPayCount === 0) {
            $this->dispatch('notify', variant: 'danger', title: 'No Tokens to Pay', message: 'This commercial has no purchased tokens to pay.');
            return;
        }

        $this->selectedCommercialForPayment = $commercial;
        $this->paymentType = 0; // Default to Wire
        $this->showPaymentModal = true;
    }

    /**
     * Process the payment
     */
    public function processPayment()
    {
        if (!$this->selectedCommercialForPayment) {
            $this->dispatch('notify', variant: 'danger', title: 'Invalid Payment', message: 'No commercial selected for payment.');
            return;
        }

        DB::transaction(function () {
            $commercial = $this->selectedCommercialForPayment;

            // Get all clubs for this commercial
            $clubIds = Club::where('commercial_id', $commercial->commercial_id)->pluck('club_id')->toArray();

            // Get all purchased tokens that haven't been paid yet for all clubs under this commercial
            $tokensToUpdate = Token::whereIn('club_id', $clubIds)
                ->whereNotNull('purchased_at')
                ->whereNull('paid_at')
                ->with(['tokenType', 'club'])
                ->get();

            if ($tokensToUpdate->isEmpty()) {
                $this->dispatch('notify', variant: 'danger', title: 'No Tokens to Pay', message: 'No purchased tokens found to pay.');
                return;
            }

            // Calculate total value with commercial commission
            $totalValue = 0;
            foreach ($tokensToUpdate as $token) {
                $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
                $club = $token->club;
                $commercialCommission = $club ? ($club->commercial_commission / 100) : 0;
                $totalValue += $tokenValue * $commercialCommission;
            }

            // Generate payment
            $paymentId = Payment::generatePaymentId();
            $paymentNumber = Payment::generatePaymentNumber();

            // Create payment record
            Payment::create([
                'payment_id' => $paymentId,
                'payment_nbr' => $paymentNumber,
                'type' => 2, // Commercial
                'type_id' => $commercial->commercial_id,
                'date' => now()->toDateString(),
                'value' => $totalValue,
                'currency' => 'EU',
                'pay_type' => $this->paymentType,
            ]);

            // Update all tokens with payment_id and paid_at
            $tokensToUpdate->each(function ($token) use ($paymentId) {
                $token->update([
                    'payment_id' => $paymentId,
                    'paid_at' => now()->toDateTimeString(),
                ]);
            });

            $this->dispatch('notify', variant: 'success', title: 'Payment Processed', message: "Payment {$paymentNumber} processed successfully for €" . number_format($totalValue, 2));
        });

        $this->closePaymentModal();
    }

    /**
     * Close payment modal
     */
    public function closePaymentModal()
    {
        $this->showPaymentModal = false;
        $this->selectedCommercialForPayment = null;
        $this->paymentType = 0;
    }

    /**
     * Show club details modal for commercial
     */
    public function viewClubDetails($commercialId)
    {
        $commercial = Commercial::findOrFail($commercialId);
        $this->selectedCommercialForDetails = $commercial;

        // Get detailed club breakdown
        $this->clubDetails = $this->calculateClubBreakdown($commercial);

        $this->showClubDetailsModal = true;
    }

    /**
     * Close club details modal
     */
    public function closeClubDetailsModal()
    {
        $this->showClubDetailsModal = false;
        $this->selectedCommercialForDetails = null;
        $this->clubDetails = [];
    }
}
