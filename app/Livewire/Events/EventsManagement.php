<?php

namespace App\Livewire\Events;

use App\Models\Event;
use App\Models\Stat;
use Livewire\Component;
use Livewire\WithPagination;

class EventsManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $level = '';
    public $sex = '';
    public $deleteId;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'level' => ['except' => ''],
        'sex' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function updatingLevel()
    {
        $this->resetPage();
    }

    public function updatingSex()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Event::with(['club', 'firstSponsor', 'secondSponsor', 'thirdSponsor']);

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('event_name', 'like', '%' . $this->search . '%')
                    ->orWhere('event_id', 'like', '%' . $this->search . '%')
                    ->orWhereHas('club', function ($q) {
                        $q->where('club_name', 'like', '%' . $this->search . '%');
                    });
            });
        }

        if ($this->status !== '') {
            $now = now();
            switch ($this->status) {
                case 'open':
                    $query->where('end_subscribe', '>=', $now);
                    break;
                case 'closed':
                    $query->where('end_subscribe', '<', $now)
                          ->where('start', '>', $now);
                    break;
                case 'in_progress':
                    $query->where('start', '<=', $now)
                          ->where('end', '>=', $now);
                    break;
                case 'completed':
                    $query->where('end', '<', $now);
                    break;
            }
        }

        if ($this->level !== '') {
            $query->where('level', $this->level);
        }

        if ($this->sex !== '') {
            $query->where('sex', $this->sex);
        }

        $events = $query->latest()->paginate(10);

        return view('livewire.events.events-management', [
            'events' => $events
        ]);
    }

    public function confirmDelete($eventId)
    {
        $this->deleteId = $eventId;
        $this->modal('confirm-event-deletion')->show();
    }

    public function deleteEvent()
    {
        $event = Event::findOrFail($this->deleteId);
        $event->delete();

        session()->flash('message', 'Event deleted successfully.');
        $this->modal('confirm-event-deletion')->close();
    }
}
