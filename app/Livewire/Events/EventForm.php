<?php

namespace App\Livewire\Events;

use App\Models\Event;
use App\Models\Club;
use App\Models\Sponsor;
use Livewire\Component;

class EventForm extends Component
{
    public $event_id;
    public $event_name;
    public $start;
    public $end;
    public $end_subscribe;
    public $club_id;
    public $level;
    public $sex;
    public $prize;
    public $currency = 'EUR';
    public $sponsor1;
    public $sponsor2;
    public $sponsor3;
    public $subscription;

    public $isEdit = false;
    public $eventId = null;

    // For searchable club select
    public $clubSearch = '';
    public $selectedClub = null;
    public $clubs = [];
    public $showClubDropdown = false;

    // For searchable sponsor selects
    public $sponsor1Search = '';
    public $selectedSponsor1 = null;
    public $sponsors1 = [];
    public $showSponsor1Dropdown = false;

    public $sponsor2Search = '';
    public $selectedSponsor2 = null;
    public $sponsors2 = [];
    public $showSponsor2Dropdown = false;

    public $sponsor3Search = '';
    public $selectedSponsor3 = null;
    public $sponsors3 = [];
    public $showSponsor3Dropdown = false;

    protected $rules = [
        'event_name' => 'required|string|max:50',
        'start' => 'required|date|after_or_equal:today',
        'end' => 'required|date|after_or_equal:start',
        'end_subscribe' => 'required|date|before_or_equal:start',
        'club_id' => 'required|string|exists:clubs,club_id',
        'level' => 'nullable|integer|in:1,2,3,4',
        'sex' => 'nullable|integer|in:1,2',
        'prize' => 'nullable|numeric|min:0|max:999999.99',
        'currency' => 'required|string|size:3',
        'sponsor1' => 'nullable|string|exists:sponsors,sponsor_id',
        'sponsor2' => 'nullable|string|exists:sponsors,sponsor_id',
        'sponsor3' => 'nullable|string|exists:sponsors,sponsor_id',
        'subscription' => 'nullable|numeric|min:0|max:9999.99',
    ];

    protected $messages = [
        'start.after_or_equal' => 'Start date must be today or later.',
        'end.after_or_equal' => 'End date must be after or equal to start date.',
        'end_subscribe.before_or_equal' => 'Subscription end date must be before or equal to start date.',
    ];

    public function mount($eventId = null)
    {
        $this->eventId = $eventId;
        $this->isEdit = !is_null($eventId);

        if ($this->isEdit) {
            $event = Event::findOrFail($eventId);
            $this->event_id = $event->event_id;
            $this->event_name = $event->event_name;
            $this->start = $event->start->format('Y-m-d');
            $this->end = $event->end->format('Y-m-d');
            $this->end_subscribe = $event->end_subscribe->format('Y-m-d');
            $this->club_id = $event->club_id;
            $this->level = $event->level;
            $this->sex = $event->sex;
            $this->prize = $event->prize;
            $this->currency = $event->currency;
            $this->sponsor1 = $event->sponsor1;
            $this->sponsor2 = $event->sponsor2;
            $this->sponsor3 = $event->sponsor3;
            $this->subscription = $event->subscription;

            // Set club search if a club is assigned
            if ($event->club_id) {
                $club = Club::find($event->club_id);
                if ($club) {
                    $this->clubSearch = $club->club_name;
                    $this->selectedClub = $club;
                }
            }

            // Set sponsor searches if sponsors are assigned
            if ($event->sponsor1) {
                $sponsor = Sponsor::find($event->sponsor1);
                if ($sponsor) {
                    $this->sponsor1Search = $sponsor->company_name;
                    $this->selectedSponsor1 = $sponsor;
                }
            }

            if ($event->sponsor2) {
                $sponsor = Sponsor::find($event->sponsor2);
                if ($sponsor) {
                    $this->sponsor2Search = $sponsor->company_name;
                    $this->selectedSponsor2 = $sponsor;
                }
            }

            if ($event->sponsor3) {
                $sponsor = Sponsor::find($event->sponsor3);
                if ($sponsor) {
                    $this->sponsor3Search = $sponsor->company_name;
                    $this->selectedSponsor3 = $sponsor;
                }
            }
        }

        $this->loadClubs();
        $this->loadSponsors1();
        $this->loadSponsors2();
        $this->loadSponsors3();
    }

    public function render()
    {
        return view('livewire.events.event-form');
    }

    /**
     * Load clubs based on search term
     */
    public function loadClubs()
    {
        $query = Club::query()
            ->where('status', 'Active')
            ->orderBy('club_name');

        if ($this->clubSearch) {
            $query->where(function ($q) {
                $q->where('club_name', 'like', '%' . $this->clubSearch . '%')
                    ->orWhere('club_id', 'like', '%' . $this->clubSearch . '%');
            });
        }

        $this->clubs = $query->limit(10)->get();
    }

    public function updatedClubSearch()
    {
        $this->loadClubs();
        $this->showClubDropdown = !empty($this->clubSearch);
    }

    public function selectClub($clubId)
    {
        $club = Club::find($clubId);
        if ($club) {
            $this->selectedClub = $club;
            $this->club_id = $club->club_id;
            $this->clubSearch = $club->club_name;
            $this->showClubDropdown = false;
        }
    }

    /**
     * Load sponsors for sponsor1 based on search term
     */
    public function loadSponsors1()
    {
        $query = Sponsor::query()
            ->where('status', 1)
            ->orderBy('company_name');

        if ($this->sponsor1Search) {
            $query->where(function ($q) {
                $q->where('company_name', 'like', '%' . $this->sponsor1Search . '%')
                    ->orWhere('sponsor_id', 'like', '%' . $this->sponsor1Search . '%');
            });
        }

        $this->sponsors1 = $query->limit(10)->get();
    }

    public function updatedSponsor1Search()
    {
        $this->loadSponsors1();
        $this->showSponsor1Dropdown = !empty($this->sponsor1Search);
    }

    public function selectSponsor1($sponsorId)
    {
        $sponsor = Sponsor::find($sponsorId);
        if ($sponsor) {
            $this->selectedSponsor1 = $sponsor;
            $this->sponsor1 = $sponsor->sponsor_id;
            $this->sponsor1Search = $sponsor->company_name;
            $this->showSponsor1Dropdown = false;
        }
    }

    public function clearSponsor1()
    {
        $this->selectedSponsor1 = null;
        $this->sponsor1 = null;
        $this->sponsor1Search = '';
        $this->showSponsor1Dropdown = false;
    }

    /**
     * Load sponsors for sponsor2 based on search term
     */
    public function loadSponsors2()
    {
        $query = Sponsor::query()
            ->where('status', 1)
            ->orderBy('company_name');

        if ($this->sponsor2Search) {
            $query->where(function ($q) {
                $q->where('company_name', 'like', '%' . $this->sponsor2Search . '%')
                    ->orWhere('sponsor_id', 'like', '%' . $this->sponsor2Search . '%');
            });
        }

        $this->sponsors2 = $query->limit(10)->get();
    }

    public function updatedSponsor2Search()
    {
        $this->loadSponsors2();
        $this->showSponsor2Dropdown = !empty($this->sponsor2Search);
    }

    public function selectSponsor2($sponsorId)
    {
        $sponsor = Sponsor::find($sponsorId);
        if ($sponsor) {
            $this->selectedSponsor2 = $sponsor;
            $this->sponsor2 = $sponsor->sponsor_id;
            $this->sponsor2Search = $sponsor->company_name;
            $this->showSponsor2Dropdown = false;
        }
    }

    public function clearSponsor2()
    {
        $this->selectedSponsor2 = null;
        $this->sponsor2 = null;
        $this->sponsor2Search = '';
        $this->showSponsor2Dropdown = false;
    }

    /**
     * Load sponsors for sponsor3 based on search term
     */
    public function loadSponsors3()
    {
        $query = Sponsor::query()
            ->where('status', 1)
            ->orderBy('company_name');

        if ($this->sponsor3Search) {
            $query->where(function ($q) {
                $q->where('company_name', 'like', '%' . $this->sponsor3Search . '%')
                    ->orWhere('sponsor_id', 'like', '%' . $this->sponsor3Search . '%');
            });
        }

        $this->sponsors3 = $query->limit(10)->get();
    }

    public function updatedSponsor3Search()
    {
        $this->loadSponsors3();
        $this->showSponsor3Dropdown = !empty($this->sponsor3Search);
    }

    public function selectSponsor3($sponsorId)
    {
        $sponsor = Sponsor::find($sponsorId);
        if ($sponsor) {
            $this->selectedSponsor3 = $sponsor;
            $this->sponsor3 = $sponsor->sponsor_id;
            $this->sponsor3Search = $sponsor->company_name;
            $this->showSponsor3Dropdown = false;
        }
    }

    public function clearSponsor3()
    {
        $this->selectedSponsor3 = null;
        $this->sponsor3 = null;
        $this->sponsor3Search = '';
        $this->showSponsor3Dropdown = false;
    }

    public function save()
    {
        $this->validate();

        if ($this->isEdit) {
            $event = Event::findOrFail($this->eventId);
            $event->event_name = $this->event_name;
            $event->start = $this->start;
            $event->end = $this->end;
            $event->end_subscribe = $this->end_subscribe;
            $event->club_id = $this->club_id;
            $event->level = $this->level;
            $event->sex = $this->sex;
            $event->prize = $this->prize;
            $event->currency = $this->currency;
            $event->sponsor1 = $this->sponsor1;
            $event->sponsor2 = $this->sponsor2;
            $event->sponsor3 = $this->sponsor3;
            $event->subscription = $this->subscription;

            $event->save();

            session()->flash('message', 'Event updated successfully.');
        } else {
            // Generate a unique event ID
            $event_id = Event::generateEventId();

            $event = new Event();
            $event->event_id = $event_id;
            $event->event_name = $this->event_name;
            $event->start = $this->start;
            $event->end = $this->end;
            $event->end_subscribe = $this->end_subscribe;
            $event->club_id = $this->club_id;
            $event->level = $this->level;
            $event->sex = $this->sex;
            $event->prize = $this->prize;
            $event->currency = $this->currency;
            $event->sponsor1 = $this->sponsor1;
            $event->sponsor2 = $this->sponsor2;
            $event->sponsor3 = $this->sponsor3;
            $event->subscription = $this->subscription;

            $event->save();

            session()->flash('message', 'Event created successfully.');
        }

        return redirect()->route('events.index');
    }
}
