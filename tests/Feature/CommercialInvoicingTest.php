<?php

namespace Tests\Feature;

use App\Livewire\Invoicing\CommercialInvoicing;
use App\Models\Commercial;
use App\Models\Club;
use App\Models\Token;
use App\Models\TokenType;
use App\Services\FirebaseDatabaseService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;
use Mockery;

class CommercialInvoicingTest extends TestCase
{
    use RefreshDatabase;

    protected Commercial $commercial;
    protected Club $club;
    protected TokenType $tokenType;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock Firebase service to avoid database connection issues
        $this->app->bind(FirebaseDatabaseService::class, function () {
            return Mockery::mock(FirebaseDatabaseService::class, function ($mock) {
                $mock->shouldReceive('updateClubCounts')->andReturn(true);
                $mock->shouldReceive('updatePlayerCounts')->andReturn(true);
            });
        });

        // Create test data
        $this->commercial = Commercial::create([
            'commercial_id' => 'COM123',
            'name' => 'Test Commercial',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 1,
            'inserted' => now(),
        ]);

        $this->club = Club::create([
            'club_id' => 'CLB123',
            'club_name' => 'Test Club',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'commercial_id' => $this->commercial->commercial_id,
            'commercial_commission' => 3.0,
            'status' => 'Active',
        ]);

        $this->tokenType = TokenType::create([
            'club_id' => $this->club->club_id,
            'value' => 10.00,
            'value_in_eur' => 10.00,
            'minutes' => 60,
        ]);
    }

    /** @test */
    public function it_can_render_commercial_invoicing_component()
    {
        Livewire::test(CommercialInvoicing::class)
            ->assertStatus(200)
            ->assertSee('Commercials Balance & Payment');
    }

    /** @test */
    public function it_displays_commercial_with_club_breakdown()
    {
        // Create a token for testing
        Token::create([
            'token_id' => 'TOK123',
            'club_id' => $this->club->club_id,
            'token_type_id' => $this->tokenType->id,
            'status' => 'Purchased',
            'purchased_at' => now(),
        ]);

        Livewire::test(CommercialInvoicing::class)
            ->assertStatus(200)
            ->assertSee($this->commercial->name)
            ->assertSee($this->club->club_name);
    }

    /** @test */
    public function it_can_open_club_details_modal()
    {
        Livewire::test(CommercialInvoicing::class)
            ->call('viewClubDetails', $this->commercial->commercial_id)
            ->assertSet('showClubDetailsModal', true)
            ->assertSet('selectedCommercialForDetails.commercial_id', $this->commercial->commercial_id);
    }

    /** @test */
    public function it_can_close_club_details_modal()
    {
        Livewire::test(CommercialInvoicing::class)
            ->call('viewClubDetails', $this->commercial->commercial_id)
            ->call('closeClubDetailsModal')
            ->assertSet('showClubDetailsModal', false)
            ->assertSet('selectedCommercialForDetails', null)
            ->assertSet('clubDetails', []);
    }

    /** @test */
    public function it_calculates_club_breakdown_correctly()
    {
        // Create tokens with different statuses
        Token::create([
            'token_id' => 'TOK001',
            'club_id' => $this->club->club_id,
            'token_type_id' => $this->tokenType->id,
            'status' => 'Purchased',
            'purchased_at' => now(),
        ]);

        Token::create([
            'token_id' => 'TOK002',
            'club_id' => $this->club->club_id,
            'token_type_id' => $this->tokenType->id,
            'status' => 'Redeemed',
            'purchased_at' => now(),
            'paid_at' => now(),
        ]);

        $component = Livewire::test(CommercialInvoicing::class);
        $commercials = $component->get('commercials');
        
        $this->assertNotEmpty($commercials);
        $commercial = $commercials->first();
        $this->assertNotEmpty($commercial->clubBreakdown);
        
        $clubData = $commercial->clubBreakdown[0];
        $this->assertEquals($this->club->club_id, $clubData['club_id']);
        $this->assertEquals($this->club->club_name, $clubData['club_name']);
        $this->assertEquals(20.00, $clubData['purchased']); // 2 tokens * 10 EUR each
    }
}
