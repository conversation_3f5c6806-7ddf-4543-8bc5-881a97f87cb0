<?php

use App\Http\Controllers\Auth\VerifyEmailController;
use App\Livewire\Auth\ConfirmPassword;
use App\Livewire\Auth\AdminForgotPassword;
use App\Livewire\Auth\AdminResetPassword;
use App\Livewire\Auth\ClubForgotPassword;
use App\Livewire\Auth\ClubResetPassword;
use App\Livewire\Auth\CommercialForgotPassword;
use App\Livewire\Auth\CommercialResetPassword;
use App\Livewire\Auth\VerifyEmail;
use Illuminate\Support\Facades\Route;

// Main domain auth routes (admin)
Route::domain(config('app.url'))->middleware('guest:admin')->group(function () {
    Route::get('forgot-password', AdminForgotPassword::class)->name('password.request');
    Route::get('reset-password/{token}', AdminResetPassword::class)->name('password.reset');
});

// Club subdomain auth routes
Route::domain('club.' . parse_url(config('app.url'), PHP_URL_HOST))->middleware('guest:club_web')->group(function () {
    Route::get('forgot-password', ClubForgotPassword::class)->name('club.password.request');
    Route::get('reset-password/{token}', ClubResetPassword::class)->name('club.password.reset');
});

// Commercial subdomain auth routes
Route::domain('commercial.' . parse_url(config('app.url'), PHP_URL_HOST))->middleware('guest:commercial_web')->group(function () {
    Route::get('forgot-password', CommercialForgotPassword::class)->name('commercial.password.request');
    Route::get('reset-password/{token}', CommercialResetPassword::class)->name('commercial.password.reset');
});

Route::middleware('auth:admin')->group(function () {
    Route::get('verify-email', VerifyEmail::class)
        ->name('verification.notice');

    Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
        ->middleware(['signed', 'throttle:6,1'])
        ->name('verification.verify');

    Route::get('confirm-password', ConfirmPassword::class)
        ->name('password.confirm');
});

// Main domain logout (admin)
Route::domain(config('app.url'))->group(function () {
    Route::post('logout', App\Livewire\Actions\Logout::class)->middleware('auth:admin')
        ->name('logout');
});
