<?php

use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\CheckPlayerStatus;
use App\Http\Middleware\ClubMiddleware;
use App\Http\Middleware\CommercialMiddleware;
use App\Http\Middleware\SetUserLocale;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->append(SetUserLocale::class);
        
        $middleware->alias([
            'check.player.status' => CheckPlayerStatus::class,
            'admin' => AdminMiddleware::class,
            'club' => ClubMiddleware::class,
            'commercial' => CommercialMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
