<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events_subscribe', function (Blueprint $table) {
            $table->string('event_id', 6);
            $table->string('player_id', 6);
            $table->date('subsc_date');
            $table->timestamps();

            // Composite primary key
            $table->primary(['event_id', 'player_id']);

            // Foreign key constraints
            $table->foreign('event_id')
                ->references('event_id')
                ->on('events')
                ->noActionOnUpdate()
                ->cascadeOnDelete();

            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events_subscribe');
    }
};
