<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->string('event_id', 6)->primary();
            $table->string('event_name', 50);
            $table->date('start');
            $table->date('end');
            $table->date('end_subscribe');
            $table->string('club_id', 6);
            $table->tinyInteger('level')->nullable();
            $table->tinyInteger('sex')->nullable();
            $table->decimal('prize', 9, 2)->nullable();
            $table->string('currency', 2)->nullable();
            $table->string('sponsor1', 6)->nullable();
            $table->string('sponsor2', 6)->nullable();
            $table->string('sponsor3', 6)->nullable();
            $table->decimal('subscription', 6, 2)->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('club_id')
                ->references('club_id')
                ->on('clubs')
                ->noActionOnUpdate()
                ->cascadeOnDelete();

            $table->foreign('sponsor1')
                ->references('sponsor_id')
                ->on('sponsors')
                ->onDelete('set null');

            $table->foreign('sponsor2')
                ->references('sponsor_id')
                ->on('sponsors')
                ->onDelete('set null');

            $table->foreign('sponsor3')
                ->references('sponsor_id')
                ->on('sponsors')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
