<div class="flex flex-col gap-6">
    <x-auth-header :title="__('Commercial - Forgot password')" :description="__('Enter your commercial email to receive a password reset link')" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <form wire:submit="sendPasswordResetLink" class="flex flex-col gap-6">
        <!-- Email Address -->
        <flux:input
            wire:model="email"
            :label="__('Commercial Email Address')"
            type="email"
            required
            autofocus
            placeholder="<EMAIL>"
        />

        <flux:button variant="primary" type="submit" class="w-full">{{ __('Email password reset link') }}</flux:button>
    </form>

    <div class="text-center">
        <a href="{{ route('commercial.login') }}" class="text-sm text-zinc-600 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-100">
            {{ __('Back to login') }}
        </a>
    </div>
</div>
