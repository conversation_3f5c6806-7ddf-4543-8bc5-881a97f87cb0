<div>
    <!-- <PERSON>er -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">
            {{ $isEdit ? __('Edit Event') : __('Create Event') }}
        </h1>
        <p class="mt-1 text-sm text-zinc-600 dark:text-zinc-400">
            {{ $isEdit ? __('Update event information') : __('Add a new event to the system') }}
        </p>
    </div>

    <!-- Flash Message -->
     @if(session()->has('message'))
        <div class="mb-6 border border-green-800 rounded-md p-4 bg-green-50 text-green-800 dark:bg-green-800 dark:text-green-100">
            {{ session('message') }}
        </div>
    @endif

    <!-- Form -->
    <div class="rounded-lg border border-zinc-200 bg-white p-6 shadow dark:border-zinc-700 dark:bg-zinc-900">
        <form wire:submit.prevent="save">
            <!-- Basic Information -->
            <div class="mb-6">
                <h3 class="mb-4 text-lg font-medium text-zinc-900 dark:text-zinc-100">{{ __('Basic Information') }}</h3>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                        <flux:input 
                            wire:model="event_name" 
                            :label="__('Event Name')" 
                            :placeholder="__('Enter event name')"
                            required 
                        />
                        @error('event_name') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <!-- Club Selection -->
                    <div class="relative">
                        <flux:input 
                            wire:model.live="clubSearch" 
                            :label="__('Club')" 
                            :placeholder="__('Search and select club')"
                            x-on:focus="$wire.set('showClubDropdown', true)"
                            required 
                        />
                        @error('club_id') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        
                        @if($showClubDropdown && count($clubs) > 0)
                            <div class="absolute z-10 mt-1 w-full bg-white border border-zinc-300 rounded-md shadow-lg dark:bg-zinc-800 dark:border-zinc-600">
                                @foreach($clubs as $club)
                                    <div wire:click="selectClub('{{ $club->club_id }}')" 
                                         class="px-4 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700 cursor-pointer">
                                        <div class="font-medium text-zinc-900 dark:text-zinc-100">{{ $club->club_name }}</div>
                                        <div class="text-sm text-zinc-500 dark:text-zinc-400">{{ $club->club_id }}</div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Event Dates -->
            <div class="mb-6">
                <h3 class="mb-4 text-lg font-medium text-zinc-900 dark:text-zinc-100">{{ __('Event Dates') }}</h3>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div>
                        <flux:input 
                            wire:model="start" 
                            type="date"
                            :label="__('Start Date')" 
                            required 
                        />
                        @error('start') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input 
                            wire:model="end" 
                            type="date"
                            :label="__('End Date')" 
                            required 
                        />
                        @error('end') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input 
                            wire:model="end_subscribe" 
                            type="date"
                            :label="__('Registration Deadline')" 
                            required 
                        />
                        @error('end_subscribe') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            <!-- Event Details -->
            <div class="mb-6">
                <h3 class="mb-4 text-lg font-medium text-zinc-900 dark:text-zinc-100">{{ __('Event Details') }}</h3>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <div>
                        <flux:select wire:model="level" :label="__('Level')" :placeholder="__('Select level')">
                            <option value="">{{ __('All Levels') }}</option>
                            <option value="1">{{ __('Beginner') }}</option>
                            <option value="2">{{ __('Intermediate') }}</option>
                            <option value="3">{{ __('Advanced') }}</option>
                            <option value="4">{{ __('Professional') }}</option>
                        </flux:select>
                        @error('level') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:select wire:model="sex" :label="__('Gender')" :placeholder="__('Select gender')">
                            <option value="">{{ __('Mixed') }}</option>
                            <option value="1">{{ __('Male') }}</option>
                            <option value="2">{{ __('Female') }}</option>
                        </flux:select>
                        @error('sex') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input 
                            wire:model="prize" 
                            type="number"
                            step="0.01"
                            min="0"
                            :label="__('Prize Amount')" 
                            :placeholder="__('0.00')"
                        />
                        @error('prize') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:input 
                            wire:model="subscription" 
                            type="number"
                            step="0.01"
                            min="0"
                            :label="__('Registration Fee')" 
                            :placeholder="__('0.00')"
                        />
                        @error('subscription') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <div class="mt-4">
                    {{-- select dropdown instead --}}
                    <flux:select wire:model="currency" :label="__('Currency')" :placeholder="__('Select currency')">
                        <option value="USD" label="United States dollar">USD</option>
                        <option value="EUR" label="Euro">EUR</option>
                        <option disabled>──────────</option>
                        <option value="AFN" label="Afghan afghani">AFN</option>
                        <option value="ALL" label="Albanian lek">ALL</option>
                        <option value="DZD" label="Algerian dinar">DZD</option>
                        <option value="AOA" label="Angolan kwanza">AOA</option>
                        <option value="ARS" label="Argentine peso">ARS</option>
                        <option value="AMD" label="Armenian dram">AMD</option>
                        <option value="AWG" label="Aruban florin">AWG</option>
                        <option value="AUD" label="Australian dollar">AUD</option>
                        <option value="AZN" label="Azerbaijani manat">AZN</option>
                        <option value="BSD" label="Bahamian dollar">BSD</option>
                        <option value="BHD" label="Bahraini dinar">BHD</option>
                        <option value="BDT" label="Bangladeshi taka">BDT</option>
                        <option value="BBD" label="Barbadian dollar">BBD</option>
                        <option value="BYN" label="Belarusian ruble">BYN</option>
                        <option value="BZD" label="Belize dollar">BZD</option>
                        <option value="BMD" label="Bermudian dollar">BMD</option>
                        <option value="BTN" label="Bhutanese ngultrum">BTN</option>
                        <option value="BOB" label="Bolivian boliviano">BOB</option>
                        <option value="BAM" label="Bosnia and Herzegovina convertible mark">BAM</option>
                        <option value="BWP" label="Botswana pula">BWP</option>
                        <option value="BRL" label="Brazilian real">BRL</option>
                        <option value="GBP" label="British pound">GBP</option>
                    </flux:select>
                    @error('currency') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>
            </div>

            <!-- Sponsors -->
            <div class="mb-6">
                <h3 class="mb-4 text-lg font-medium text-zinc-900 dark:text-zinc-100">{{ __('Sponsors') }}</h3>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <!-- Sponsor 1 -->
                    <div class="relative">
                        <div class="flex items-center space-x-2">
                            <flux:input
                                wire:model.live="sponsor1Search"
                                :label="__('Sponsor 1')"
                                :placeholder="__('Search sponsor')"
                                x-on:focus="$wire.set('showSponsor1Dropdown', true)"
                                class="flex-1"
                            />
                            @if($selectedSponsor1)
                                <flux:button
                                    variant="ghost"
                                    size="xs"
                                    icon="x-mark"
                                    wire:click="clearSponsor1"
                                    class="mt-6"
                                />
                            @endif
                        </div>
                        @error('sponsor1') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror

                        @if($showSponsor1Dropdown && count($sponsors1) > 0)
                            <div class="absolute z-10 mt-1 w-full bg-white border border-zinc-300 rounded-md shadow-lg dark:bg-zinc-800 dark:border-zinc-600">
                                @foreach($sponsors1 as $sponsor)
                                    <div wire:click="selectSponsor1('{{ $sponsor->sponsor_id }}')"
                                         class="px-4 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700 cursor-pointer">
                                        <div class="font-medium text-zinc-900 dark:text-zinc-100">{{ $sponsor->company_name }}</div>
                                        <div class="text-sm text-zinc-500 dark:text-zinc-400">{{ $sponsor->sponsor_id }}</div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Sponsor 2 -->
                    <div class="relative">
                        <div class="flex items-center space-x-2">
                            <flux:input
                                wire:model.live="sponsor2Search"
                                :label="__('Sponsor 2')"
                                :placeholder="__('Search sponsor')"
                                x-on:focus="$wire.set('showSponsor2Dropdown', true)"
                                class="flex-1"
                            />
                            @if($selectedSponsor2)
                                <flux:button
                                    variant="ghost"
                                    size="xs"
                                    icon="x-mark"
                                    wire:click="clearSponsor2"
                                    class="mt-6"
                                />
                            @endif
                        </div>
                        @error('sponsor2') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror

                        @if($showSponsor2Dropdown && count($sponsors2) > 0)
                            <div class="absolute z-10 mt-1 w-full bg-white border border-zinc-300 rounded-md shadow-lg dark:bg-zinc-800 dark:border-zinc-600">
                                @foreach($sponsors2 as $sponsor)
                                    <div wire:click="selectSponsor2('{{ $sponsor->sponsor_id }}')"
                                         class="px-4 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700 cursor-pointer">
                                        <div class="font-medium text-zinc-900 dark:text-zinc-100">{{ $sponsor->company_name }}</div>
                                        <div class="text-sm text-zinc-500 dark:text-zinc-400">{{ $sponsor->sponsor_id }}</div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Sponsor 3 -->
                    <div class="relative">
                        <div class="flex items-center space-x-2">
                            <flux:input
                                wire:model.live="sponsor3Search"
                                :label="__('Sponsor 3')"
                                :placeholder="__('Search sponsor')"
                                x-on:focus="$wire.set('showSponsor3Dropdown', true)"
                                class="flex-1"
                            />
                            @if($selectedSponsor3)
                                <flux:button
                                    variant="ghost"
                                    size="xs"
                                    icon="x-mark"
                                    wire:click="clearSponsor3"
                                    class="mt-6"
                                />
                            @endif
                        </div>
                        @error('sponsor3') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror

                        @if($showSponsor3Dropdown && count($sponsors3) > 0)
                            <div class="absolute z-10 mt-1 w-full bg-white border border-zinc-300 rounded-md shadow-lg dark:bg-zinc-800 dark:border-zinc-600">
                                @foreach($sponsors3 as $sponsor)
                                    <div wire:click="selectSponsor3('{{ $sponsor->sponsor_id }}')"
                                         class="px-4 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700 cursor-pointer">
                                        <div class="font-medium text-zinc-900 dark:text-zinc-100">{{ $sponsor->company_name }}</div>
                                        <div class="text-sm text-zinc-500 dark:text-zinc-400">{{ $sponsor->sponsor_id }}</div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="outline" :href="route('events.index')" wire:navigate>
                    {{ __('Cancel') }}
                </flux:button>

                <flux:button variant="primary" type="submit">
                    {{ $isEdit ? __('Update Event') : __('Create Event') }}
                </flux:button>
            </div>
        </form>
    </div>
</div>

<script>
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const dropdowns = [
            { dropdown: document.querySelector('[x-show="showClubDropdown"]'), input: document.getElementById('clubSearch') },
            { dropdown: document.querySelector('[x-show="showSponsor1Dropdown"]'), input: document.getElementById('sponsor1Search') },
            { dropdown: document.querySelector('[x-show="showSponsor2Dropdown"]'), input: document.getElementById('sponsor2Search') },
            { dropdown: document.querySelector('[x-show="showSponsor3Dropdown"]'), input: document.getElementById('sponsor3Search') }
        ];

        dropdowns.forEach(({ dropdown, input }) => {
            if (dropdown && !dropdown.contains(event.target) && event.target !== input) {
                // Close the respective dropdown
                if (dropdown.getAttribute('x-show') === 'showClubDropdown') {
                    @this.set('showClubDropdown', false);
                } else if (dropdown.getAttribute('x-show') === 'showSponsor1Dropdown') {
                    @this.set('showSponsor1Dropdown', false);
                } else if (dropdown.getAttribute('x-show') === 'showSponsor2Dropdown') {
                    @this.set('showSponsor2Dropdown', false);
                } else if (dropdown.getAttribute('x-show') === 'showSponsor3Dropdown') {
                    @this.set('showSponsor3Dropdown', false);
                }
            }
        });
    });
</script>
