<div>
    <!-- <PERSON>er -->
    <div class="mb-6 flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-zinc-900 dark:text-zinc-100">{{ __('Events Management') }}</h1>
            <p class="mt-1 text-sm text-zinc-600 dark:text-zinc-400">
                {{ __('Manage events, tournaments, and competitions') }}
            </p>
        </div>

        <div class="flex space-x-3">
            <flux:button variant="primary" :href="route('events.create')" wire:navigate icon="plus">
                {{ __('Add Event') }}
            </flux:button>
        </div>
    </div>

    <!-- Flash Message -->
    @if(session()->has('message'))
        <div class="mb-6 border border-green-800 rounded-md p-4 bg-green-50 text-green-800 dark:bg-green-800 dark:text-green-100">
            {{ session('message') }}
        </div>
    @endif

    <!-- Search and Filter Section -->
    <div class="mb-6 rounded-lg border border-zinc-200 bg-white p-4 dark:border-zinc-700 dark:bg-zinc-900">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-5">
            <flux:input wire:model.live="search" :placeholder="__('Search events...')" />

            <div>
                <flux:select wire:model.live="status" :placeholder="__('Filter by status')">
                    <option value="">{{ __('All Statuses') }}</option>
                    <option value="open">{{ __('Open for Registration') }}</option>
                    <option value="closed">{{ __('Registration Closed') }}</option>
                    <option value="in_progress">{{ __('In Progress') }}</option>
                    <option value="completed">{{ __('Completed') }}</option>
                </flux:select>
            </div>

            <div>
                <flux:select wire:model.live="level" :placeholder="__('Filter by level')">
                    <option value="">{{ __('All Levels') }}</option>
                    <option value="1">{{ __('Beginner') }}</option>
                    <option value="2">{{ __('Intermediate') }}</option>
                    <option value="3">{{ __('Advanced') }}</option>
                    <option value="4">{{ __('Professional') }}</option>
                </flux:select>
            </div>

            <div>
                <flux:select wire:model.live="sex" :placeholder="__('Filter by gender')">
                    <option value="">{{ __('All Genders') }}</option>
                    <option value="1">{{ __('Male') }}</option>
                    <option value="2">{{ __('Female') }}</option>
                </flux:select>
            </div>
        </div>
    </div>

    <!-- Events Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 bg-white shadow dark:border-zinc-700 dark:bg-zinc-900">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-800">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Event ID') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Event Details') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Club') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Dates') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Details') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                    @forelse ($events as $event)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-800">
                            <td class="whitespace-nowrap px-6 py-4 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                {{ $event->event_id }}
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="text-zinc-900 dark:text-zinc-100 font-medium">{{ $event->event_name }}</div>
                                <div class="text-zinc-500 dark:text-zinc-400 text-xs">
                                    {{ $event->level_text }} • {{ $event->sex_text }}
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="text-zinc-900 dark:text-zinc-100">{{ $event->club->club_name ?? 'N/A' }}</div>
                                <div class="text-zinc-500 dark:text-zinc-400 text-xs">{{ $event->club->club_id ?? '' }}</div>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="text-zinc-900 dark:text-zinc-100">{{ $event->start->format('d/m/Y') }} - {{ $event->end->format('d/m/Y') }}</div>
                                <div class="text-zinc-500 dark:text-zinc-400 text-xs">
                                    {{ __('Registration until') }}: {{ $event->end_subscribe->format('d/m/Y') }}
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                @if($event->prize)
                                    <div class="text-zinc-900 dark:text-zinc-100">
                                        {{ __('Prize') }}: {{ number_format($event->prize, 2) }} {{ $event->currency }}
                                    </div>
                                @endif
                                @if($event->subscription)
                                    <div class="text-zinc-500 dark:text-zinc-400 text-xs">
                                        {{ __('Fee') }}: {{ number_format($event->subscription, 2) }} {{ $event->currency }}
                                    </div>
                                @endif
                                <div class="text-zinc-500 dark:text-zinc-400 text-xs">
                                    {{ $event->subscription_count }} {{ __('participants') }}
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if ($event->status === 'Open for Registration')
                                    <flux:badge variant="success">{{ __('Open') }}</flux:badge>
                                @elseif ($event->status === 'Registration Closed')
                                    <flux:badge variant="warning">{{ __('Closed') }}</flux:badge>
                                @elseif ($event->status === 'In Progress')
                                    <flux:badge variant="info">{{ __('In Progress') }}</flux:badge>
                                @else
                                    <flux:badge variant="neutral">{{ __('Completed') }}</flux:badge>
                                @endif
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    <flux:modal.trigger name="view-event-{{ $event->event_id }}">
                                        <flux:button variant="ghost" size="xs" icon="eye" />
                                    </flux:modal.trigger>

                                    <flux:button variant="ghost" size="xs" icon="pencil"
                                        :href="route('events.edit', $event->event_id)" wire:navigate />

                                    <flux:button variant="ghost" size="xs" icon="trash"
                                        wire:click="confirmDelete('{{ $event->event_id }}')" />
                                </div>

                                <!-- View Event Modal -->
                                <flux:modal name="view-event-{{ $event->event_id }}" title="{{ __('Event Details') }}" class="max-w-lg w-full">
                                    <div class="space-y-4">
                                        <div class="grid grid-cols-2 gap-4">
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Event ID') }}:</span>
                                                <span>{{ $event->event_id }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Event Name') }}:</span>
                                                <span>{{ $event->event_name }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Club') }}:</span>
                                                <span>{{ $event->club->club_name ?? 'N/A' }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Level') }}:</span>
                                                <span>{{ $event->level_text }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Gender') }}:</span>
                                                <span>{{ $event->sex_text }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Status') }}:</span>
                                                <span>{{ $event->status }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Start Date') }}:</span>
                                                <span>{{ $event->start->format('d/m/Y') }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('End Date') }}:</span>
                                                <span>{{ $event->end->format('d/m/Y') }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Registration Until') }}:</span>
                                                <span>{{ $event->end_subscribe->format('d/m/Y') }}</span>
                                            </div>
                                            <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span class="font-medium dark:text-white text-zinc-800">{{ __('Participants') }}:</span>
                                                <span>{{ $event->subscription_count }}</span>
                                            </div>
                                            @if($event->prize)
                                                <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                    <span class="font-medium dark:text-white text-zinc-800">{{ __('Prize') }}:</span>
                                                    <span>{{ number_format($event->prize, 2) }} {{ $event->currency }}</span>
                                                </div>
                                            @endif
                                            @if($event->subscription)
                                                <div class="dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                    <span class="font-medium dark:text-white text-zinc-800">{{ __('Registration Fee') }}:</span>
                                                    <span>{{ number_format($event->subscription, 2) }} {{ $event->currency }}</span>
                                                </div>
                                            @endif
                                        </div>

                                        @if($event->firstSponsor || $event->secondSponsor || $event->thirdSponsor)
                                            <div class="border-t pt-4">
                                                <h4 class="font-medium dark:text-white text-zinc-800 mb-2">{{ __('Sponsors') }}</h4>
                                                <div class="space-y-2">
                                                    @if($event->firstSponsor)
                                                        <div class="text-sm text-zinc-600 dark:text-zinc-300">
                                                            {{ __('Sponsor 1') }}: {{ $event->firstSponsor->company_name }}
                                                        </div>
                                                    @endif
                                                    @if($event->secondSponsor)
                                                        <div class="text-sm text-zinc-600 dark:text-zinc-300">
                                                            {{ __('Sponsor 2') }}: {{ $event->secondSponsor->company_name }}
                                                        </div>
                                                    @endif
                                                    @if($event->thirdSponsor)
                                                        <div class="text-sm text-zinc-600 dark:text-zinc-300">
                                                            {{ __('Sponsor 3') }}: {{ $event->thirdSponsor->company_name }}
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </flux:modal>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-zinc-500 dark:text-zinc-400">
                                {{ __('No events found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if ($events->hasPages())
            <div class="border-t border-zinc-200 bg-white px-4 py-3 dark:border-zinc-700 dark:bg-zinc-900">
                {{ $events->links() }}
            </div>
        @endif
    </div>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="confirm-event-deletion" title="{{ __('Confirm Deletion') }}" class="max-w-lg w-full">
        <div class="space-y-4">
            <p class="text-zinc-600 dark:text-zinc-400">
                {{ __('Are you sure you want to delete this event? This action cannot be undone and will also remove all participant registrations.') }}
            </p>

            <div class="flex justify-end space-x-3">
                <flux:button variant="outline" x-on:click="$wire.modal('confirm-event-deletion').hide()">
                    {{ __('Cancel') }}
                </flux:button>

                <flux:button variant="danger" wire:click="deleteEvent">
                    {{ __('Delete Event') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
